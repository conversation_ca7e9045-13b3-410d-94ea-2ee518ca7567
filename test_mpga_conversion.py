#!/usr/bin/env python3
"""
Test script specifically for MPGA file conversion
"""

import requests
import json
import sys
import os
from datetime import datetime

def test_mpga_conversion(api_url, mpga_url):
    """
    Test MPGA file conversion
    
    Args:
        api_url (str): Base URL of the MP3 Converter API
        mpga_url (str): URL of the MPGA file to convert
    """
    print(f"Testing MPGA conversion...")
    print(f"API URL: {api_url}")
    print(f"MPGA URL: {mpga_url}")
    print("-" * 50)
    
    try:
        # First, let's check what the URL returns
        print("1. Checking source file...")
        head_response = requests.head(mpga_url, timeout=10)
        print(f"   Status Code: {head_response.status_code}")
        print(f"   Content-Type: {head_response.headers.get('content-type', 'Unknown')}")
        print(f"   Content-Length: {head_response.headers.get('content-length', 'Unknown')} bytes")
        
        if head_response.status_code != 200:
            print(f"   ⚠️  Warning: Source file returned status {head_response.status_code}")
        
        # Test the conversion
        print("\n2. Testing conversion...")
        payload = {"url": mpga_url}
        headers = {"Content-Type": "application/json"}
        
        start_time = datetime.now()
        response = requests.post(
            f"{api_url}/convert",
            data=json.dumps(payload),
            headers=headers,
            timeout=120  # 2 minutes timeout
        )
        end_time = datetime.now()
        
        conversion_time = (end_time - start_time).total_seconds()
        print(f"   Conversion time: {conversion_time:.2f} seconds")
        
        if response.status_code == 200:
            # Check response headers
            content_type = response.headers.get('content-type', '')
            content_length = len(response.content)
            
            print(f"   ✅ Conversion successful!")
            print(f"   Response Content-Type: {content_type}")
            print(f"   Output file size: {content_length:,} bytes")
            
            # Save the converted file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"converted_mpga_{timestamp}.mp3"
            
            with open(output_filename, 'wb') as f:
                f.write(response.content)
            
            print(f"   💾 Saved as: {output_filename}")
            
            # Verify it's a valid MP3 file (basic check)
            if response.content.startswith(b'ID3') or response.content[0:2] == b'\xff\xfb':
                print(f"   ✅ Output appears to be a valid MP3 file")
            else:
                print(f"   ⚠️  Warning: Output may not be a valid MP3 file")
            
            return True
            
        else:
            print(f"   ❌ Conversion failed: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error message: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("   ❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Could not connect to API")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_api_health(api_url):
    """Test API health"""
    try:
        response = requests.get(f"{api_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Health: {data['status']}")
            return True
        else:
            print(f"❌ API Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not reach API: {e}")
        return False

def main():
    print("=" * 60)
    print("MPGA to MP3 Conversion Test")
    print("=" * 60)
    
    # Configuration
    API_URL = os.environ.get('API_URL', 'http://localhost:5000')
    
    # You can set your MPGA URL here or via environment variable
    MPGA_URL = os.environ.get('MPGA_URL')
    
    if not MPGA_URL:
        print("Please provide an MPGA URL to test.")
        print("You can:")
        print("1. Set environment variable: export MPGA_URL='http://example.com/audio.mpga'")
        print("2. Edit this script and set MPGA_URL variable")
        print("3. Pass it as command line argument")
        
        if len(sys.argv) > 1:
            MPGA_URL = sys.argv[1]
        else:
            # Example MPGA URL (replace with your actual URL)
            MPGA_URL = input("Enter MPGA URL to test: ").strip()
            
        if not MPGA_URL:
            print("No URL provided. Exiting.")
            sys.exit(1)
    
    print(f"API URL: {API_URL}")
    print(f"MPGA URL: {MPGA_URL}")
    print()
    
    # Test API health
    if not test_api_health(API_URL):
        print("\nAPI is not healthy. Please start the service first.")
        print("Run: python app.py")
        sys.exit(1)
    
    print()
    
    # Test MPGA conversion
    success = test_mpga_conversion(API_URL, MPGA_URL)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ MPGA conversion test completed successfully!")
        print("Check the current directory for the converted MP3 file.")
    else:
        print("❌ MPGA conversion test failed.")
        print("Check the API logs for more details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
