#!/usr/bin/env python3
"""
Example usage of the MP3 Converter API
"""

import requests
import json

def convert_video_to_mp3(api_url, video_url, output_filename):
    """
    Convert a video file to MP3 using the API
    
    Args:
        api_url (str): Base URL of the MP3 Converter API
        video_url (str): URL of the video file to convert
        output_filename (str): Local filename to save the MP3
    """
    print(f"Converting: {video_url}")
    print(f"API: {api_url}")
    
    # Prepare the request
    convert_url = f"{api_url}/convert"
    payload = {"url": video_url}
    headers = {"Content-Type": "application/json"}
    
    try:
        # Make the conversion request
        print("Sending conversion request...")
        response = requests.post(
            convert_url,
            data=json.dumps(payload),
            headers=headers,
            timeout=300  # 5 minutes timeout
        )
        
        if response.status_code == 200:
            # Save the MP3 file
            with open(output_filename, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"✓ Conversion successful!")
            print(f"✓ Saved as: {output_filename} ({file_size:,} bytes)")
            
        else:
            print(f"✗ Conversion failed: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"Response: {response.text}")
                
    except requests.exceptions.Timeout:
        print("✗ Request timed out - the file might be too large or conversion too slow")
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the API - make sure the service is running")
    except Exception as e:
        print(f"✗ Error: {e}")

def check_api_health(api_url):
    """Check if the API is healthy"""
    try:
        response = requests.get(f"{api_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ API is healthy: {data['status']}")
            return True
        else:
            print(f"✗ API health check failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Could not reach API: {e}")
        return False

def main():
    # Configuration
    API_URL = "http://localhost:5000"  # Change this to your server's URL
    
    # Example video URLs (replace with your own)
    example_videos = [
        "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
        "https://file-examples.com/storage/fe68c1b7d4c2b0b7b0b7b0b/2017/10/file_example_MP4_480_1_5MG.mp4"
    ]
    
    print("=" * 60)
    print("MP3 Converter API - Example Usage")
    print("=" * 60)
    
    # Check API health first
    if not check_api_health(API_URL):
        print("\nPlease make sure the MP3 Converter API is running.")
        print("You can start it with: python app.py")
        return
    
    print()
    
    # Convert example videos
    for i, video_url in enumerate(example_videos, 1):
        output_filename = f"converted_audio_{i}.mp3"
        print(f"\n--- Example {i} ---")
        convert_video_to_mp3(API_URL, video_url, output_filename)
    
    print("\n" + "=" * 60)
    print("Example completed!")
    print("Check the current directory for converted MP3 files.")

if __name__ == "__main__":
    main()
