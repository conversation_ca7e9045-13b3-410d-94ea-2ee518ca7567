"""
Configuration settings for MP3 Converter API
"""

import os
import tempfile


class Config:
    """Base configuration class"""
    
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # File handling settings
    TEMP_DIR = os.environ.get('TEMP_DIR', os.path.join(tempfile.gettempdir(), 'mp3_converter'))
    MAX_FILE_SIZE = int(os.environ.get('MAX_FILE_SIZE', 100 * 1024 * 1024))  # 100MB default
    DOWNLOAD_TIMEOUT = int(os.environ.get('DOWNLOAD_TIMEOUT', 300))  # 5 minutes default
    
    # Supported file extensions for input
    SUPPORTED_EXTENSIONS = {
        '.mpeg', '.mpg', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'
    }
    
    # FFmpeg settings
    FFMPEG_QUALITY = os.environ.get('FFMPEG_QUALITY', '192k')  # Audio bitrate
    FFMPEG_TIMEOUT = int(os.environ.get('FFMPEG_TIMEOUT', 600))  # 10 minutes default


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    TEMP_DIR = os.path.join(tempfile.gettempdir(), 'mp3_converter_test')


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
