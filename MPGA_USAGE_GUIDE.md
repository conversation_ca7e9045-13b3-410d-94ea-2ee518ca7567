# MPGA to MP3 Conversion Guide

This guide specifically covers converting MPGA (MPEG Audio) files to MP3 format using the MP3 Converter API.

## What is MPGA?

MPGA (MPEG Audio) is an audio file format that uses MPEG compression. Files with `.mpga` extension typically contain:
- **MIME Type**: `audio/mpeg`
- **Encoding**: MPEG-1 or MPEG-2 audio compression
- **Quality**: Variable, depending on the source

## API Usage for MPGA Files

### Basic Conversion Request

```bash
curl -X POST http://localhost:5000/convert \
  -H "Content-Type: application/json" \
  -d '{"url": "http://example.com/audio.mpga"}' \
  --output converted.mp3
```

### Python Example

```python
import requests
import json

def convert_mpga_to_mp3(api_url, mpga_url, output_file):
    """Convert MPGA file to MP3"""
    
    payload = {"url": mpga_url}
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(
        f"{api_url}/convert",
        data=json.dumps(payload),
        headers=headers,
        timeout=120
    )
    
    if response.status_code == 200:
        with open(output_file, 'wb') as f:
            f.write(response.content)
        print(f"✅ Converted successfully: {output_file}")
        return True
    else:
        print(f"❌ Conversion failed: {response.status_code}")
        return False

# Usage
api_url = "http://localhost:5000"
mpga_url = "http://example.com/audio.mpga"
convert_mpga_to_mp3(api_url, mpga_url, "output.mp3")
```

### JavaScript Example

```javascript
async function convertMpgaToMp3(apiUrl, mpgaUrl) {
    try {
        const response = await fetch(`${apiUrl}/convert`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: mpgaUrl })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            // Create download link
            const a = document.createElement('a');
            a.href = url;
            a.download = 'converted.mp3';
            a.click();
            
            console.log('✅ Conversion successful');
        } else {
            console.error('❌ Conversion failed:', response.status);
        }
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

// Usage
convertMpgaToMp3('http://localhost:5000', 'http://example.com/audio.mpga');
```

## Testing Your MPGA Files

### Using the Test Script

```bash
# Test with your MPGA URL
python test_mpga_conversion.py http://example.com/your-audio.mpga

# Or set environment variable
export MPGA_URL="http://example.com/your-audio.mpga"
python test_mpga_conversion.py
```

### Manual Testing Steps

1. **Check your MPGA file first:**
   ```bash
   curl -I http://example.com/your-audio.mpga
   ```
   Look for:
   - `HTTP/1.1 200 OK`
   - `Content-Type: audio/mpeg`
   - `Content-Length: [file-size]`

2. **Test the API health:**
   ```bash
   curl http://localhost:5000/health
   ```

3. **Perform the conversion:**
   ```bash
   curl -X POST http://localhost:5000/convert \
     -H "Content-Type: application/json" \
     -d '{"url": "http://example.com/your-audio.mpga"}' \
     --output test-output.mp3
   ```

4. **Verify the output:**
   ```bash
   file test-output.mp3
   # Should show: test-output.mp3: Audio file with ID3 version 2.4.0
   ```

## Common Issues and Solutions

### Issue 1: "Invalid URL" Error
**Problem**: The API rejects your MPGA URL
**Solutions**:
- Ensure the URL is accessible (test with `curl -I`)
- Check that the URL returns HTTP 200 status
- Verify the URL doesn't require authentication

### Issue 2: "File too large" Error
**Problem**: MPGA file exceeds size limit
**Solutions**:
- Check file size: default limit is 100MB
- Increase `MAX_FILE_SIZE` environment variable
- Use a smaller file for testing

### Issue 3: "Conversion failed" Error
**Problem**: FFmpeg cannot process the MPGA file
**Solutions**:
- Verify the file is actually audio (not corrupted)
- Check FFmpeg logs: `sudo journalctl -u mp3converter -f`
- Try with a different MPGA file

### Issue 4: Empty or Invalid MP3 Output
**Problem**: Conversion completes but output is invalid
**Solutions**:
- Check if input file has audio streams
- Verify FFmpeg is properly installed
- Try increasing conversion timeout

## Quality Settings

### Default Settings
- **Bitrate**: 192 kbps
- **Sample Rate**: 44.1 kHz
- **Channels**: Stereo (2)
- **Format**: MP3 with LAME encoder

### Customizing Quality
Set environment variables before starting the API:

```bash
# High quality (256 kbps)
export FFMPEG_QUALITY="256k"

# Lower quality (128 kbps) for smaller files
export FFMPEG_QUALITY="128k"

# Restart the service
sudo systemctl restart mp3converter
```

## Performance Considerations

### File Size vs. Conversion Time
- **Small files** (< 10MB): ~5-15 seconds
- **Medium files** (10-50MB): ~15-60 seconds  
- **Large files** (50-100MB): ~1-5 minutes

### Optimizing Performance
1. **Increase timeout** for large files:
   ```bash
   export FFMPEG_TIMEOUT="1200"  # 20 minutes
   ```

2. **Use faster quality settings**:
   ```bash
   export FFMPEG_QUALITY="128k"  # Faster than 256k
   ```

3. **Monitor system resources**:
   ```bash
   htop  # Check CPU and memory usage during conversion
   ```

## Batch Processing

### Converting Multiple MPGA Files

```python
import requests
import json
import os

def batch_convert_mpga(api_url, mpga_urls):
    """Convert multiple MPGA files"""
    
    results = []
    
    for i, url in enumerate(mpga_urls, 1):
        print(f"Converting file {i}/{len(mpga_urls)}: {url}")
        
        try:
            response = requests.post(
                f"{api_url}/convert",
                json={"url": url},
                timeout=300
            )
            
            if response.status_code == 200:
                filename = f"converted_{i:03d}.mp3"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                results.append({"url": url, "status": "success", "file": filename})
                print(f"  ✅ Success: {filename}")
            else:
                results.append({"url": url, "status": "failed", "error": response.status_code})
                print(f"  ❌ Failed: HTTP {response.status_code}")
                
        except Exception as e:
            results.append({"url": url, "status": "error", "error": str(e)})
            print(f"  ❌ Error: {e}")
    
    return results

# Usage
mpga_files = [
    "http://example.com/audio1.mpga",
    "http://example.com/audio2.mpga",
    "http://example.com/audio3.mpga"
]

results = batch_convert_mpga("http://localhost:5000", mpga_files)

# Print summary
successful = sum(1 for r in results if r["status"] == "success")
print(f"\nBatch conversion complete: {successful}/{len(mpga_files)} successful")
```

## Integration Examples

### Web Application Integration

```html
<!DOCTYPE html>
<html>
<head>
    <title>MPGA to MP3 Converter</title>
</head>
<body>
    <h1>Convert MPGA to MP3</h1>
    
    <form id="convertForm">
        <label for="mpgaUrl">MPGA File URL:</label>
        <input type="url" id="mpgaUrl" required placeholder="http://example.com/audio.mpga">
        <button type="submit">Convert</button>
    </form>
    
    <div id="status"></div>
    
    <script>
        document.getElementById('convertForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const url = document.getElementById('mpgaUrl').value;
            const status = document.getElementById('status');
            
            status.innerHTML = 'Converting...';
            
            try {
                const response = await fetch('/convert', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: url })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const downloadUrl = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = 'converted.mp3';
                    a.click();
                    
                    status.innerHTML = '✅ Conversion successful! Download started.';
                } else {
                    status.innerHTML = `❌ Conversion failed: ${response.status}`;
                }
            } catch (error) {
                status.innerHTML = `❌ Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
```

## Troubleshooting Checklist

- [ ] API is running and healthy (`curl http://localhost:5000/health`)
- [ ] MPGA URL is accessible (`curl -I [your-mpga-url]`)
- [ ] File size is within limits (default: 100MB)
- [ ] Content-Type is `audio/mpeg` or similar
- [ ] FFmpeg is installed and working
- [ ] Sufficient disk space in temp directory
- [ ] Network connectivity for downloading files
- [ ] Proper timeout settings for large files

## Support

For MPGA-specific issues:
1. Test with the provided `test_mpga_conversion.py` script
2. Check the API logs: `sudo journalctl -u mp3converter -f`
3. Verify your MPGA file with: `ffprobe [your-file.mpga]`
4. Test with a known working MPGA file first
