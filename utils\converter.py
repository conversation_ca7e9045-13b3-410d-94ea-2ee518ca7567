"""
Audio conversion utility using FFmpeg for MP3 Converter API
"""

import os
import subprocess
import logging
from config import Config

logger = logging.getLogger(__name__)


def check_ffmpeg():
    """
    Check if FFmpeg is available on the system
    
    Returns:
        bool: True if FFmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(
            ['ffmpeg', '-version'],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        return False


def get_file_info(file_path):
    """
    Get information about media file using FFprobe
    
    Args:
        file_path (str): Path to media file
        
    Returns:
        dict: File information or None if failed
    """
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            file_path
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            import json
            return json.loads(result.stdout)
        else:
            logger.warning(f"FFprobe failed for {file_path}: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return None


def convert_to_mp3(input_file, temp_dir):
    """
    Convert media file to MP3 format using FFmpeg
    
    Args:
        input_file (str): Path to input media file
        temp_dir (str): Temporary directory for output
        
    Returns:
        str: Path to converted MP3 file
        
    Raises:
        RuntimeError: If FFmpeg is not available
        subprocess.SubprocessError: If conversion fails
        IOError: If file operations fail
    """
    # Check if FFmpeg is available
    if not check_ffmpeg():
        raise RuntimeError("FFmpeg is not installed or not available in PATH")
    
    # Generate output filename
    input_basename = os.path.splitext(os.path.basename(input_file))[0]
    output_file = os.path.join(temp_dir, f"{input_basename}.mp3")
    
    logger.info(f"Converting {input_file} to {output_file}")
    
    # Get input file info for logging
    file_info = get_file_info(input_file)
    if file_info:
        format_info = file_info.get('format', {})
        duration = format_info.get('duration', 'unknown')
        size = format_info.get('size', 'unknown')
        logger.info(f"Input file - Duration: {duration}s, Size: {size} bytes")
    
    try:
        # Build FFmpeg command
        cmd = [
            'ffmpeg',
            '-i', input_file,           # Input file
            '-vn',                      # No video
            '-acodec', 'mp3',          # Audio codec
            '-ab', Config.FFMPEG_QUALITY,  # Audio bitrate
            '-ar', '44100',            # Sample rate
            '-ac', '2',                # Stereo
            '-y',                      # Overwrite output file
            output_file
        ]
        
        logger.info(f"Running FFmpeg command: {' '.join(cmd)}")
        
        # Run conversion
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=Config.FFMPEG_TIMEOUT
        )
        
        if result.returncode != 0:
            error_msg = f"FFmpeg conversion failed: {result.stderr}"
            logger.error(error_msg)
            raise subprocess.SubprocessError(error_msg)
        
        # Verify output file was created
        if not os.path.exists(output_file):
            raise IOError("Output MP3 file was not created")
        
        output_size = os.path.getsize(output_file)
        if output_size == 0:
            raise IOError("Output MP3 file is empty")
        
        logger.info(f"Conversion successful. Output size: {output_size} bytes")
        
        # Log conversion details
        if result.stderr:
            # FFmpeg writes progress info to stderr
            logger.debug(f"FFmpeg output: {result.stderr}")
        
        return output_file
        
    except subprocess.TimeoutExpired:
        error_msg = f"FFmpeg conversion timed out after {Config.FFMPEG_TIMEOUT} seconds"
        logger.error(error_msg)
        # Clean up partial output file
        if os.path.exists(output_file):
            os.remove(output_file)
        raise subprocess.SubprocessError(error_msg)
        
    except Exception as e:
        logger.error(f"Conversion error: {e}")
        # Clean up partial output file
        if os.path.exists(output_file):
            os.remove(output_file)
        raise


def validate_audio_file(file_path):
    """
    Validate that file contains audio streams
    
    Args:
        file_path (str): Path to media file
        
    Returns:
        bool: True if file contains audio, False otherwise
    """
    file_info = get_file_info(file_path)
    if not file_info:
        return False
    
    streams = file_info.get('streams', [])
    for stream in streams:
        if stream.get('codec_type') == 'audio':
            return True
    
    return False
