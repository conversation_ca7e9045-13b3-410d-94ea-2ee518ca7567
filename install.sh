#!/bin/bash

# MP3 Converter API - Ubuntu Installation Script
# This script installs and sets up the MP3 Converter API on Ubuntu Server

set -e  # Exit on any error

echo "========================================="
echo "MP3 Converter API Installation Script"
echo "========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons."
   print_status "Please run as a regular user with sudo privileges."
   exit 1
fi

# Check Ubuntu version
print_status "Checking Ubuntu version..."
if ! lsb_release -d | grep -q "Ubuntu"; then
    print_warning "This script is designed for Ubuntu. Proceeding anyway..."
fi

# Update system packages
print_status "Updating system packages..."
sudo apt-get update

# Install system dependencies
print_status "Installing system dependencies..."
sudo apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    ffmpeg \
    git \
    curl \
    nginx \
    supervisor

# Verify FFmpeg installation
print_status "Verifying FFmpeg installation..."
if ! command -v ffmpeg &> /dev/null; then
    print_error "FFmpeg installation failed"
    exit 1
fi

ffmpeg_version=$(ffmpeg -version | head -n1)
print_status "FFmpeg installed: $ffmpeg_version"

# Create application directory
APP_DIR="/opt/mp3converter"
print_status "Creating application directory: $APP_DIR"
sudo mkdir -p $APP_DIR
sudo chown $USER:$USER $APP_DIR

# Copy application files (assuming script is run from project directory)
print_status "Copying application files..."
cp -r . $APP_DIR/
cd $APP_DIR

# Create virtual environment
print_status "Creating Python virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create temp directory
print_status "Creating temporary directory..."
sudo mkdir -p /tmp/mp3_converter
sudo chown $USER:$USER /tmp/mp3_converter

# Create systemd service file
print_status "Creating systemd service..."
sudo tee /etc/systemd/system/mp3converter.service > /dev/null <<EOF
[Unit]
Description=MP3 Converter API
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin
Environment=FLASK_ENV=production
Environment=TEMP_DIR=/tmp/mp3_converter
ExecStart=$APP_DIR/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 2 --timeout 300 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# Create nginx configuration
print_status "Creating nginx configuration..."
sudo tee /etc/nginx/sites-available/mp3converter > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    client_max_body_size 200M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
}
EOF

# Enable nginx site
print_status "Enabling nginx site..."
sudo ln -sf /etc/nginx/sites-available/mp3converter /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
print_status "Testing nginx configuration..."
sudo nginx -t

# Enable and start services
print_status "Enabling and starting services..."
sudo systemctl daemon-reload
sudo systemctl enable mp3converter
sudo systemctl start mp3converter
sudo systemctl enable nginx
sudo systemctl restart nginx

# Check service status
print_status "Checking service status..."
sleep 3

if sudo systemctl is-active --quiet mp3converter; then
    print_status "MP3 Converter service is running"
else
    print_error "MP3 Converter service failed to start"
    sudo systemctl status mp3converter
    exit 1
fi

if sudo systemctl is-active --quiet nginx; then
    print_status "Nginx service is running"
else
    print_error "Nginx service failed to start"
    sudo systemctl status nginx
    exit 1
fi

# Test the API
print_status "Testing API endpoint..."
sleep 2
if curl -f http://localhost/health > /dev/null 2>&1; then
    print_status "API health check passed"
else
    print_warning "API health check failed - service may still be starting"
fi

# Display completion message
echo ""
echo "========================================="
print_status "Installation completed successfully!"
echo "========================================="
echo ""
print_status "Service Information:"
echo "  - Application directory: $APP_DIR"
echo "  - Service name: mp3converter"
echo "  - API endpoint: http://your-server-ip/"
echo "  - Health check: http://your-server-ip/health"
echo ""
print_status "Useful commands:"
echo "  - Check service status: sudo systemctl status mp3converter"
echo "  - View logs: sudo journalctl -u mp3converter -f"
echo "  - Restart service: sudo systemctl restart mp3converter"
echo "  - Check nginx status: sudo systemctl status nginx"
echo ""
print_status "API Usage:"
echo "  POST /convert"
echo "  Content-Type: application/json"
echo "  Body: {\"url\": \"http://example.com/video.mpeg\"}"
echo ""

deactivate
