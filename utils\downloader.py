"""
File downloader utility for MP3 Converter API
"""

import os
import requests
import logging
from urllib.parse import urlparse, unquote
from config import Config

logger = logging.getLogger(__name__)


def validate_url(url):
    """
    Validate URL format and scheme
    
    Args:
        url (str): URL to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        parsed = urlparse(url)
        return parsed.scheme in ('http', 'https') and parsed.netloc
    except Exception:
        return False


def get_filename_from_url(url):
    """
    Extract filename from URL
    
    Args:
        url (str): URL to extract filename from
        
    Returns:
        str: Extracted filename or generated name
    """
    try:
        parsed = urlparse(url)
        path = unquote(parsed.path)
        filename = os.path.basename(path)
        
        if filename and '.' in filename:
            return filename
        else:
            # Generate filename based on URL
            return f"download_{hash(url) % 10000}.mpeg"
    except Exception:
        return "download.mpeg"


def download_file(url, temp_dir):
    """
    Download file from URL to temporary directory
    
    Args:
        url (str): URL to download from
        temp_dir (str): Temporary directory to save file
        
    Returns:
        str: Path to downloaded file
        
    Raises:
        ValueError: If URL is invalid
        requests.RequestException: If download fails
        IOError: If file operations fail
    """
    # Validate URL
    if not validate_url(url):
        raise ValueError(f"Invalid URL: {url}")
    
    # Get filename
    filename = get_filename_from_url(url)
    file_path = os.path.join(temp_dir, filename)
    
    logger.info(f"Downloading {url} to {file_path}")
    
    try:
        # Configure request headers
        headers = {
            'User-Agent': 'MP3Converter/1.0 (Audio Conversion Service)'
        }
        
        # Download with streaming to handle large files
        with requests.get(
            url, 
            headers=headers,
            stream=True,
            timeout=Config.DOWNLOAD_TIMEOUT
        ) as response:
            response.raise_for_status()
            
            # Check content length if available
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) > Config.MAX_FILE_SIZE:
                raise ValueError(f"File too large: {content_length} bytes (max: {Config.MAX_FILE_SIZE})")
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            logger.info(f"Content type: {content_type}")
            
            # Download file in chunks
            downloaded_size = 0
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # Check size limit during download
                        if downloaded_size > Config.MAX_FILE_SIZE:
                            os.remove(file_path)
                            raise ValueError(f"File too large: {downloaded_size} bytes (max: {Config.MAX_FILE_SIZE})")
            
            logger.info(f"Downloaded {downloaded_size} bytes to {file_path}")
            
            # Verify file was created and has content
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                raise IOError("Downloaded file is empty or was not created")
            
            return file_path
            
    except requests.RequestException as e:
        logger.error(f"Download failed: {e}")
        # Clean up partial file
        if os.path.exists(file_path):
            os.remove(file_path)
        raise
    except Exception as e:
        logger.error(f"Download error: {e}")
        # Clean up partial file
        if os.path.exists(file_path):
            os.remove(file_path)
        raise
