version: '3.8'

services:
  mp3converter:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - MAX_FILE_SIZE=104857600  # 100MB
      - DOWNLOAD_TIMEOUT=300
      - FFMPEG_TIMEOUT=600
      - FFMPEG_QUALITY=192k
    volumes:
      - mp3_temp:/tmp/mp3_converter
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - mp3converter
    restart: unless-stopped

volumes:
  mp3_temp:
