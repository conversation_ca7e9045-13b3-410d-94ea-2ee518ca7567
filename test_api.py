#!/usr/bin/env python3
"""
Simple test script for MP3 Converter API
"""

import requests
import json
import sys
import os

def test_health_endpoint(base_url):
    """Test the health endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Health check passed: {data['status']}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False

def test_convert_endpoint(base_url, test_url):
    """Test the convert endpoint"""
    print(f"Testing convert endpoint with URL: {test_url}")
    try:
        payload = {"url": test_url}
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(
            f"{base_url}/convert",
            data=json.dumps(payload),
            headers=headers,
            timeout=60
        )
        
        if response.status_code == 200:
            # Check if response is an MP3 file
            content_type = response.headers.get('content-type', '')
            if 'audio' in content_type.lower():
                print(f"✓ Conversion successful, received audio file ({len(response.content)} bytes)")
                
                # Save the file for verification
                output_file = "test_output.mp3"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                print(f"✓ Saved converted file as: {output_file}")
                return True
            else:
                print(f"✗ Unexpected content type: {content_type}")
                return False
        else:
            print(f"✗ Conversion failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Conversion error: {e}")
        return False

def test_invalid_requests(base_url):
    """Test invalid requests"""
    print("Testing invalid requests...")
    
    # Test missing URL
    try:
        response = requests.post(
            f"{base_url}/convert",
            json={},
            timeout=10
        )
        if response.status_code == 400:
            print("✓ Correctly rejected request with missing URL")
        else:
            print(f"✗ Unexpected response for missing URL: {response.status_code}")
    except Exception as e:
        print(f"✗ Error testing missing URL: {e}")
    
    # Test invalid URL
    try:
        response = requests.post(
            f"{base_url}/convert",
            json={"url": "not-a-valid-url"},
            timeout=10
        )
        if response.status_code == 400:
            print("✓ Correctly rejected invalid URL")
        else:
            print(f"✗ Unexpected response for invalid URL: {response.status_code}")
    except Exception as e:
        print(f"✗ Error testing invalid URL: {e}")

def main():
    # Configuration
    base_url = os.environ.get('API_URL', 'http://localhost:5000')
    test_url = os.environ.get('TEST_URL', 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4')
    
    print("=" * 50)
    print("MP3 Converter API Test")
    print("=" * 50)
    print(f"API URL: {base_url}")
    print(f"Test URL: {test_url}")
    print()
    
    # Run tests
    health_ok = test_health_endpoint(base_url)
    print()
    
    if health_ok:
        convert_ok = test_convert_endpoint(base_url, test_url)
        print()
        
        test_invalid_requests(base_url)
        print()
        
        if convert_ok:
            print("✓ All tests passed!")
            sys.exit(0)
        else:
            print("✗ Some tests failed")
            sys.exit(1)
    else:
        print("✗ Health check failed, skipping other tests")
        sys.exit(1)

if __name__ == "__main__":
    main()
