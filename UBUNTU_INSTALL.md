# Ubuntu Server Installation Guide

This guide provides step-by-step instructions for installing the MP3 Converter API on Ubuntu Server.

## Quick Installation (Recommended)

### 1. Download and Run the Installation Script

```bash
# Clone the repository
git clone <your-repository-url>
cd mp3Converter

# Make the script executable
chmod +x install.sh

# Run the installation script
./install.sh
```

The script will automatically:
- Install all system dependencies (Python, FFmpeg, nginx)
- Set up a Python virtual environment
- Install Python packages
- Create and start systemd services
- Configure nginx reverse proxy
- Test the installation

### 2. Verify Installation

After the script completes, test the API:

```bash
# Check service status
sudo systemctl status mp3converter

# Test the health endpoint
curl http://localhost/health

# Test conversion (replace with a real video URL)
curl -X POST http://localhost/convert \
  -H "Content-Type: application/json" \
  -d '{"url": "http://example.com/video.mp4"}' \
  --output test.mp3
```

## Manual Installation

If you prefer to install manually or the script fails:

### 1. Install System Dependencies

```bash
# Update package list
sudo apt-get update

# Install required packages
sudo apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    ffmpeg \
    git \
    curl \
    nginx \
    supervisor
```

### 2. Verify FFmpeg Installation

```bash
ffmpeg -version
```

### 3. Set Up the Application

```bash
# Create application directory
sudo mkdir -p /opt/mp3converter
sudo chown $USER:$USER /opt/mp3converter

# Clone and setup
git clone <your-repository-url> /opt/mp3converter
cd /opt/mp3converter

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Create Systemd Service

Create `/etc/systemd/system/mp3converter.service`:

```ini
[Unit]
Description=MP3 Converter API
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/opt/mp3converter
Environment=PATH=/opt/mp3converter/venv/bin
Environment=FLASK_ENV=production
Environment=TEMP_DIR=/tmp/mp3_converter
ExecStart=/opt/mp3converter/venv/bin/gunicorn --bind 127.0.0.1:5000 --workers 2 --timeout 300 app:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

Replace `your-username` with your actual username.

### 5. Configure Nginx

Create `/etc/nginx/sites-available/mp3converter`:

```nginx
server {
    listen 80;
    server_name _;
    
    client_max_body_size 200M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/mp3converter /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
```

### 6. Start Services

```bash
# Create temp directory
sudo mkdir -p /tmp/mp3_converter
sudo chown $USER:$USER /tmp/mp3_converter

# Enable and start services
sudo systemctl daemon-reload
sudo systemctl enable mp3converter
sudo systemctl start mp3converter
sudo systemctl restart nginx
```

### 7. Verify Installation

```bash
# Check service status
sudo systemctl status mp3converter
sudo systemctl status nginx

# Test API
curl http://localhost/health
```

## Docker Installation (Alternative)

If you prefer Docker:

### 1. Using Docker Compose

```bash
# Clone repository
git clone <your-repository-url>
cd mp3Converter

# Start with Docker Compose
docker-compose up -d
```

### 2. Using Docker Only

```bash
# Build image
docker build -t mp3converter .

# Run container
docker run -d -p 5000:5000 --name mp3converter mp3converter
```

## Configuration

### Environment Variables

You can customize the application by setting environment variables:

```bash
# Edit the systemd service file
sudo systemctl edit mp3converter
```

Add environment variables:

```ini
[Service]
Environment=MAX_FILE_SIZE=209715200
Environment=DOWNLOAD_TIMEOUT=600
Environment=FFMPEG_QUALITY=256k
```

### Common Configuration Options

- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 100MB)
- `DOWNLOAD_TIMEOUT`: Download timeout in seconds (default: 300)
- `FFMPEG_QUALITY`: Audio bitrate (default: 192k)
- `FFMPEG_TIMEOUT`: Conversion timeout in seconds (default: 600)

## Troubleshooting

### Service Won't Start

```bash
# Check logs
sudo journalctl -u mp3converter -n 50

# Check if port is in use
sudo netstat -tlnp | grep :5000
```

### FFmpeg Issues

```bash
# Verify FFmpeg installation
ffmpeg -version

# Reinstall if needed
sudo apt-get install --reinstall ffmpeg
```

### Permission Issues

```bash
# Fix temp directory permissions
sudo mkdir -p /tmp/mp3_converter
sudo chown $USER:$USER /tmp/mp3_converter

# Fix application directory permissions
sudo chown -R $USER:$USER /opt/mp3converter
```

### Nginx Issues

```bash
# Test nginx configuration
sudo nginx -t

# Check nginx logs
sudo tail -f /var/log/nginx/error.log
```

## Security Considerations

### Firewall Configuration

```bash
# Allow HTTP traffic
sudo ufw allow 80/tcp

# If using HTTPS
sudo ufw allow 443/tcp

# Enable firewall
sudo ufw enable
```

### SSL/HTTPS Setup (Optional)

For production use, consider setting up SSL with Let's Encrypt:

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Get certificate (replace your-domain.com)
sudo certbot --nginx -d your-domain.com
```

## Monitoring

### Log Files

- Application logs: `sudo journalctl -u mp3converter -f`
- Nginx access logs: `/var/log/nginx/access.log`
- Nginx error logs: `/var/log/nginx/error.log`

### Health Monitoring

Set up a monitoring script to check the health endpoint:

```bash
#!/bin/bash
# health_check.sh
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "API is healthy"
else
    echo "API is down - restarting service"
    sudo systemctl restart mp3converter
fi
```

Add to crontab for regular checks:

```bash
# Check every 5 minutes
*/5 * * * * /path/to/health_check.sh
```

## Support

If you encounter issues:

1. Check the logs: `sudo journalctl -u mp3converter -f`
2. Verify all dependencies are installed
3. Ensure FFmpeg is working: `ffmpeg -version`
4. Check file permissions on temp directory
5. Verify network connectivity for downloading files
