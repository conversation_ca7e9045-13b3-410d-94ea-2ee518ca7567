#!/usr/bin/env python3
"""
Test script to verify URL download without FFmpeg conversion
"""

import requests
import json
import os
import tempfile
from datetime import datetime

def test_download_only(url):
    """Test downloading the file without conversion"""
    print("=" * 60)
    print("Download Test (No Conversion)")
    print("=" * 60)
    print(f"Testing URL: {url}")
    print()
    
    try:
        # Check file headers first
        print("1. Checking file headers...")
        head_response = requests.head(url, timeout=10)
        print(f"   Status Code: {head_response.status_code}")
        print(f"   Content-Type: {head_response.headers.get('content-type', 'Unknown')}")
        print(f"   Content-Length: {head_response.headers.get('content-length', 'Unknown')} bytes")
        
        if head_response.status_code != 200:
            print(f"   ❌ File not accessible: HTTP {head_response.status_code}")
            return False
        
        # Download the file
        print("\n2. Downloading file...")
        start_time = datetime.now()
        
        response = requests.get(url, timeout=60)
        
        end_time = datetime.now()
        download_time = (end_time - start_time).total_seconds()
        
        if response.status_code == 200:
            file_size = len(response.content)
            print(f"   ✅ Download successful!")
            print(f"   File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            print(f"   Download time: {download_time:.2f} seconds")
            print(f"   Speed: {(file_size/1024)/download_time:.1f} KB/s")
            
            # Save the file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"downloaded_audio_{timestamp}.mpga"
            
            with open(filename, 'wb') as f:
                f.write(response.content)
            
            print(f"   💾 Saved as: {filename}")
            
            # Basic file analysis
            print("\n3. File analysis...")
            
            # Check file signature
            if len(response.content) > 10:
                signature = response.content[:10]
                print(f"   File signature: {signature.hex()}")
                
                # Check for common audio signatures
                if signature.startswith(b'ID3'):
                    print("   ✅ Appears to be MP3 with ID3 tags")
                elif signature[0:2] == b'\xff\xfb' or signature[0:2] == b'\xff\xfa':
                    print("   ✅ Appears to be MPEG audio")
                elif signature.startswith(b'RIFF'):
                    print("   ✅ Appears to be WAV audio")
                else:
                    print("   ℹ️  Unknown audio format signature")
            
            # Content type validation
            content_type = response.headers.get('content-type', '').lower()
            if 'audio' in content_type:
                print(f"   ✅ Valid audio content type: {content_type}")
            else:
                print(f"   ⚠️  Unexpected content type: {content_type}")
            
            return True
            
        else:
            print(f"   ❌ Download failed: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("   ❌ Download timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection error")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    # Test the specific URL
    test_url = "https://lahajati.ai/audio_file?S3=audio_generate/Voice-04-08-2025-68908879b287f.mp3"
    
    success = test_download_only(test_url)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Download test completed successfully!")
        print("\nWhat this means:")
        print("• Your URL is valid and accessible")
        print("• The file is properly formatted audio")
        print("• The API can download it successfully")
        print("• On Ubuntu with FFmpeg, conversion would work")
        print("\nNext steps:")
        print("• Deploy to Ubuntu server with FFmpeg")
        print("• The conversion will work automatically")
    else:
        print("❌ Download test failed")
        print("Check the URL and try again")

if __name__ == "__main__":
    main()
