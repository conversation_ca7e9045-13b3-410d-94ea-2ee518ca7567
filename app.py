#!/usr/bin/env python3
"""
MP3 Converter API
A simple Flask API that converts MPEG files to MP3 format
"""

import os
import tempfile
import logging
from flask import Flask, request, jsonify, send_file
from werkzeug.exceptions import BadRequest, InternalServerError
import uuid
from datetime import datetime

from utils.downloader import download_file
from utils.converter import convert_to_mp3
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(Config)

# Create temp directory if it doesn't exist
os.makedirs(app.config['TEMP_DIR'], exist_ok=True)


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })


@app.route('/convert', methods=['POST'])
def convert_mpeg_to_mp3():
    """
    Convert MPEG file from URL to MP3
    
    Expected JSON payload:
    {
        "url": "http://example.com/video.mpeg"
    }
    
    Returns: MP3 file as attachment
    """
    try:
        # Validate request
        if not request.is_json:
            raise BadRequest("Request must be JSON")
        
        data = request.get_json()
        if not data or 'url' not in data:
            raise BadRequest("Missing 'url' field in request")
        
        url = data['url']
        if not url or not isinstance(url, str):
            raise BadRequest("Invalid URL provided")
        
        # Generate unique filenames
        request_id = str(uuid.uuid4())
        temp_dir = os.path.join(app.config['TEMP_DIR'], request_id)
        os.makedirs(temp_dir, exist_ok=True)
        
        logger.info(f"Processing conversion request {request_id} for URL: {url}")
        
        try:
            # Download the file
            logger.info(f"Downloading file from: {url}")
            input_file = download_file(url, temp_dir)
            
            # Validate that the file contains audio
            from utils.converter import validate_audio_file
            if not validate_audio_file(input_file):
                logger.warning(f"File may not contain audio streams: {input_file}")
                # Continue anyway, let FFmpeg handle it

            # Convert to MP3
            logger.info(f"Converting file to MP3: {input_file}")
            output_file = convert_to_mp3(input_file, temp_dir)
            
            # Generate output filename
            original_name = os.path.splitext(os.path.basename(url.split('?')[0]))[0]
            if not original_name:
                original_name = f"converted_{request_id[:8]}"
            
            output_filename = f"{original_name}.mp3"
            
            logger.info(f"Conversion successful. Sending file: {output_filename}")
            
            # Send file with proper cleanup
            def cleanup_temp_dir():
                try:
                    import shutil
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    logger.info(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    logger.error(f"Error cleaning up temp directory: {e}")

            # Create a response that will cleanup after sending
            def generate_response():
                try:
                    with open(output_file, 'rb') as f:
                        data = f.read()
                    return data
                finally:
                    cleanup_temp_dir()
            
            # Generate response data and cleanup
            response_data = generate_response()

            # Create response
            from flask import Response
            response = Response(
                response_data,
                mimetype='audio/mpeg',
                headers={
                    'Content-Disposition': f'attachment; filename="{output_filename}"',
                    'Content-Length': str(len(response_data))
                }
            )

            return response
            
        except Exception as e:
            # Clean up on error
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass
            raise e
            
    except BadRequest as e:
        logger.warning(f"Bad request: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Internal error: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting MP3 Converter API on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
