# MP3 Converter API

A simple Flask-based REST API that converts MPEG video files to MP3 audio format. The API accepts a URL to an MPEG file, downloads it, converts it to MP3 using FFmpeg, and returns the converted audio file.

## Features

- **Simple REST API**: Single endpoint for conversion
- **URL-based input**: Provide a URL to any MPEG file
- **Automatic conversion**: Uses FFmpeg for high-quality audio extraction
- **File validation**: Validates input files and URLs
- **Error handling**: Comprehensive error handling and logging
- **Configurable**: Environment-based configuration
- **Production ready**: Includes Docker support and systemd service
- **Security**: File size limits, timeout protection, and input validation

## API Endpoints

### POST /convert
Converts an MPEG file from a URL to MP3 format.

**Request:**
```json
{
    "url": "http://example.com/video.mpeg"
}
```

**Response:**
- Success: MP3 file download (Content-Type: audio/mpeg)
- Error: JSON error message with appropriate HTTP status code

**Example using curl:**
```bash
curl -X POST http://localhost:5000/convert \
  -H "Content-Type: application/json" \
  -d '{"url": "http://example.com/sample.mpeg"}' \
  --output converted.mp3
```

### GET /health
Health check endpoint for monitoring.

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000000",
    "version": "1.0.0"
}
```

## Installation on Ubuntu Server

### Automated Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd mp3Converter
   ```

2. **Make the install script executable:**
   ```bash
   chmod +x install.sh
   ```

3. **Run the installation script:**
   ```bash
   ./install.sh
   ```

The script will:
- Install system dependencies (Python, FFmpeg, nginx)
- Set up a Python virtual environment
- Install Python dependencies
- Create systemd service
- Configure nginx reverse proxy
- Start all services

### Manual Installation

1. **Update system and install dependencies:**
   ```bash
   sudo apt-get update
   sudo apt-get install -y python3 python3-pip python3-venv ffmpeg git nginx
   ```

2. **Clone and setup the application:**
   ```bash
   git clone <repository-url>
   cd mp3Converter
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **Create systemd service:**
   ```bash
   sudo cp mp3converter.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable mp3converter
   sudo systemctl start mp3converter
   ```

4. **Configure nginx (optional):**
   ```bash
   sudo cp nginx.conf /etc/nginx/sites-available/mp3converter
   sudo ln -s /etc/nginx/sites-available/mp3converter /etc/nginx/sites-enabled/
   sudo systemctl restart nginx
   ```

## Docker Installation

### Build and run with Docker:

```bash
# Build the image
docker build -t mp3converter .

# Run the container
docker run -d -p 5000:5000 --name mp3converter mp3converter
```

### Using Docker Compose:

```yaml
version: '3.8'
services:
  mp3converter:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - MAX_FILE_SIZE=104857600  # 100MB
    volumes:
      - /tmp/mp3_converter:/tmp/mp3_converter
    restart: unless-stopped
```

## Configuration

The application can be configured using environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `5000` | Port to run the application on |
| `DEBUG` | `False` | Enable debug mode |
| `SECRET_KEY` | `dev-secret-key-change-in-production` | Flask secret key |
| `TEMP_DIR` | `/tmp/mp3_converter` | Temporary directory for file processing |
| `MAX_FILE_SIZE` | `104857600` | Maximum file size in bytes (100MB) |
| `DOWNLOAD_TIMEOUT` | `300` | Download timeout in seconds |
| `FFMPEG_QUALITY` | `192k` | Audio bitrate for MP3 conversion |
| `FFMPEG_TIMEOUT` | `600` | FFmpeg conversion timeout in seconds |

## Development

### Local Development Setup:

```bash
# Clone the repository
git clone <repository-url>
cd mp3Converter

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run in development mode
export FLASK_ENV=development
export DEBUG=true
python app.py
```

### Running Tests:

```bash
# Install test dependencies
pip install pytest pytest-cov requests-mock

# Run tests
pytest tests/ -v --cov=.
```

## Service Management

### Systemd Commands:
```bash
# Check service status
sudo systemctl status mp3converter

# View logs
sudo journalctl -u mp3converter -f

# Restart service
sudo systemctl restart mp3converter

# Stop service
sudo systemctl stop mp3converter
```

### Nginx Commands:
```bash
# Check nginx status
sudo systemctl status nginx

# Test configuration
sudo nginx -t

# Reload configuration
sudo systemctl reload nginx
```

## Troubleshooting

### Common Issues:

1. **FFmpeg not found:**
   ```bash
   sudo apt-get install ffmpeg
   ```

2. **Permission denied on temp directory:**
   ```bash
   sudo mkdir -p /tmp/mp3_converter
   sudo chown $USER:$USER /tmp/mp3_converter
   ```

3. **Service won't start:**
   ```bash
   sudo journalctl -u mp3converter -n 50
   ```

4. **Large file conversion fails:**
   - Increase `MAX_FILE_SIZE` and `FFMPEG_TIMEOUT` environment variables
   - Check available disk space in temp directory

### Log Locations:
- Application logs: `sudo journalctl -u mp3converter`
- Nginx logs: `/var/log/nginx/access.log` and `/var/log/nginx/error.log`

## Security Considerations

- The application validates URLs and file types
- File size limits prevent abuse
- Temporary files are automatically cleaned up
- The service runs as a non-root user
- Input sanitization prevents path traversal attacks

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Ensure all dependencies are properly installed
4. Verify FFmpeg is working: `ffmpeg -version`
